export { welcomeTemplate } from './welcome';
export { passwordResetTemplate } from './password-reset';
// Add other templates here as they are created

import type { EmailTemplate } from '../templating.server';

export type TemplateName = 'welcome' | 'passwordReset';

export const templates: Record<TemplateName, EmailTemplate> = {
  welcome: welcomeTemplate,
  passwordReset: passwordResetTemplate,
};

export const getTemplate = (name: TemplateName): EmailTemplate | undefined => {
  return templates[name];
};
