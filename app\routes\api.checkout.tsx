import type { ActionFunctionArgs } from "@remix-run/cloudflare";
import { respData, respErr } from "~/lib/api/resp";
import { getSnowId } from "~/lib/auth/hash";
import { getStripeClient } from "~/lib/payment/stripe.server";
import { insertOrder, updateOrderSession } from "~/models/order";
import type { Order } from "~/models/order";
import { findUserByUuid } from "~/models/user";
import { getPricingPage } from "~/services/page";
import type { PricingItem } from "~/services/page";
import { requireUser } from "~/lib/auth/middleware.server";

export async function action({ request, context }: ActionFunctionArgs) {
  if (request.method !== "POST") {
    return respErr("Method not allowed");
  }

  try {
    const db = context.db;
    if (!db) {
      return respErr("Database not available");
    }
    const body = (await request.json()) as {
      credits?: number;
      currency?: string;
      amount?: number;
      interval?: "month" | "year" | "one-time";
      product_id?: string;
      product_name?: string;
      valid_months?: number;
      cancel_url?: string;
    };
    let {
      credits,
      currency,
      amount,
      interval,
      product_id,
      product_name,
      valid_months,
      cancel_url,
    } = body;

    // Set default cancel URL
    if (!cancel_url) {
      cancel_url = `${context.cloudflare.env.WEB_URL || "http://localhost:3000"}/payment-failure`;
    }

    if (!amount || !interval || !currency || !product_id || !product_name || !valid_months) {
      return respErr("invalid params");
    }

    // Validate checkout params against pricing configuration
    const page = await getPricingPage("en");
    if (!page || !page.pricing || !page.pricing.items) {
      return respErr("invalid pricing table");
    }

    const item = page.pricing.items.find((item: PricingItem) => item.product_id === product_id);

    if (
      !item ||
      !item.amount ||
      !item.interval ||
      !item.currency ||
      item.amount !== amount ||
      item.interval !== interval ||
      item.currency !== currency
    ) {
      return respErr("invalid checkout params");
    }

    if (!["year", "month", "one-time"].includes(interval)) {
      return respErr("invalid interval");
    }

    const is_subscription = interval === "month" || interval === "year";

    if (interval === "year" && valid_months !== 12) {
      return respErr("invalid valid_months");
    }

    if (interval === "month" && valid_months !== 1) {
      return respErr("invalid valid_months");
    }

    const user = await requireUser(request);
    const user_uuid = user.id;
    const user_email = user.email;

    const order_no = getSnowId();
    const currentDate = new Date();
    const created_at = currentDate.toISOString();

    let expired_at = "";
    const timePeriod = new Date(currentDate);
    timePeriod.setMonth(currentDate.getMonth() + valid_months);

    const timePeriodMillis = timePeriod.getTime();
    let delayTimeMillis = 0;

    // Add 24 hours delay for subscriptions
    if (is_subscription) {
      delayTimeMillis = 24 * 60 * 60 * 1000;
    }

    const newTimeMillis = timePeriodMillis + delayTimeMillis;
    const newDate = new Date(newTimeMillis);
    expired_at = newDate.toISOString();

    const order: Order = {
      order_no: order_no,
      created_at: created_at,
      user_uuid: user_uuid,
      user_email: user_email,
      amount: amount,
      interval: interval,
      expired_at: expired_at,
      status: "created",
      credits: credits,
      currency: currency,
      product_id: product_id,
      product_name: product_name,
      valid_months: valid_months,
    };

    await insertOrder(order, db);

    const stripeSecretKey = context.cloudflare.env.STRIPE_SECRET_KEY;
    if (!stripeSecretKey) {
      return respErr("Stripe not configured");
    }

    const stripe = getStripeClient(stripeSecretKey);

    const sessionOptions: any = {
      payment_method_types: ["card"],
      line_items: [
        {
          price_data: {
            currency: currency,
            product_data: {
              name: product_name,
            },
            unit_amount: amount,
            recurring: is_subscription
              ? {
                  interval: interval,
                }
              : undefined,
          },
          quantity: 1,
        },
      ],
      allow_promotion_codes: true,
      metadata: {
        project: context.cloudflare.env.PROJECT_NAME || "Remix App",
        product_name: product_name,
        order_no: order_no.toString(),
        user_email: user_email,
        credits: credits?.toString() || "0",
        user_uuid: user_uuid,
      },
      mode: is_subscription ? "subscription" : "payment",
      success_url: `${context.cloudflare.env.WEB_URL || "http://localhost:3000"}/payment-success?session_id={CHECKOUT_SESSION_ID}`,
      cancel_url: cancel_url,
    };

    if (user_email) {
      sessionOptions.customer_email = user_email;
    }

    if (is_subscription) {
      sessionOptions.subscription_data = {
        metadata: sessionOptions.metadata,
      };
    }

    // Support for Chinese payment methods
    if (currency === "cny") {
      sessionOptions.payment_method_types = ["wechat_pay", "alipay", "card"];
      sessionOptions.payment_method_options = {
        wechat_pay: {
          client: "web",
        },
        alipay: {},
      };
    }

    const order_detail = JSON.stringify(sessionOptions);
    const session = await stripe.checkout.sessions.create(sessionOptions);

    const stripe_session_id = session.id;
    await updateOrderSession(order_no, stripe_session_id, order_detail, db);

    return respData({
      public_key: context.cloudflare.env.STRIPE_PUBLIC_KEY,
      order_no: order_no,
      session_id: stripe_session_id,
    });
  } catch (e: any) {
    console.log("checkout failed: ", e);
    return respErr("checkout failed: " + e.message);
  }
}
