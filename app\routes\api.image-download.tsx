// app/routes/api.image-download.tsx

import type { LoaderFunctionArgs } from "@remix-run/cloudflare";
import { ImageStorageService } from "~/lib/storage/image-storage.server";
import { requireUser } from "~/lib/auth/middleware.server"; // Assuming this utility for auth
import { db as defaultDb } from "~/lib/db/db"; // Default Drizzle instance
import { generatedImages } from "~/lib/db/schema";
import { eq, and, isNull } from "drizzle-orm";

export async function loader({ request, context }: LoaderFunctionArgs) {
  const R2_BUCKET = context.cloudflare.env.R2_BUCKET;
  if (!R2_BUCKET) {
    return new Response("R2 bucket not configured", { status: 503 });
  }

  const db = context.db || defaultDb; // Use context.db if available, else defaultDb
  if (!db) {
      return new Response("Database not available", { status: 503 });
  }

  // Initialize ImageStorageService without publicUrl, as we are serving it, not generating a public link here
  const imageService = new ImageStorageService(R2_BUCKET);

  try {
    const user = await requireUser(request); // Authenticate the user

    const url = new URL(request.url);
    const key = url.searchParams.get("key");

    if (!key) {
      return new Response("Missing image key", { status: 400 });
    }

    // Fetch image metadata from DB
    const imageRecord = await db.query.generatedImages.findFirst({
      where: and(
        eq(generatedImages.r2Key, key),
        isNull(generatedImages.deletedAt) // Ensure image is not soft-deleted
      ),
      columns: {
        userId: true,
        imageFormat: true,
        // other fields could be selected if needed for more complex access logic
      }
    });

    if (!imageRecord) {
      return new Response("Image not found or has been deleted", { status: 404 });
    }

    // Access Control Logic:
    // 1. Check if the authenticated user owns the image.
    // 2. Allow access if imageRecord.userId is null (e.g. system-wide images, if applicable).
    //    This part can be adjusted based on application's needs for public/unowned images.
    //    For now, if userId is on the record, it must match. If no userId, it's considered accessible by any authenticated user.
    if (imageRecord.userId && imageRecord.userId !== user.id) {
      return new Response("Forbidden: You do not have access to this image", { status: 403 });
    }

    // Get the image object from R2
    const r2Object = await imageService.getImage(key);

    if (!r2Object || !r2Object.body) {
      console.error(`Image record found in DB for key ${key}, but not in R2. Potential inconsistency.`);
      return new Response("Image data not found in storage", { status: 404 });
    }

    // Prepare response headers
    const headers = new Headers();
    const contentType = r2Object.httpMetadata?.contentType || imageRecord.imageFormat || "application/octet-stream";
    headers.set("Content-Type", contentType);
    headers.set("Content-Length", r2Object.size.toString());
    headers.set("Cache-Control", "public, max-age=31536000, immutable"); // Cache aggressively
    headers.set("ETag", r2Object.etag);
    headers.set("Content-Disposition", "inline"); // Display image directly in browser

    // Handle conditional requests (If-None-Match)
    const ifNoneMatch = request.headers.get("if-none-match");
    if (ifNoneMatch === r2Object.etag) {
      return new Response(null, { status: 304 }); // Not Modified
    }

    return new Response(r2Object.body, {
      status: 200,
      headers,
    });

  } catch (error) {
    // Specific handling for auth errors thrown by requireUser
    if (error instanceof Response && (error.status === 401 || error.status === 302)) {
        return error; // Re-throw the auth error/redirect response
    }
    console.error("Image download error:", error);
    const errorMessage = error instanceof Error ? error.message : "Unknown error";
    return new Response(`Failed to download image: ${errorMessage}`, { status: 500 });
  }
}
