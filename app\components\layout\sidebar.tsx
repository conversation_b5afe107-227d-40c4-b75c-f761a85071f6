import { Link, useLocation, useSearchParams } from "@remix-run/react";
import {
  ChevronDown,
  ChevronRight,
  MessageSquare,
  Plus,
  Settings,
  Sparkles,
  Trash2,
  X,
} from "lucide-react";
import { type ReactNode, useEffect, useState } from "react";
import { Badge } from "~/components/ui/badge";
import { Button } from "~/components/ui/button";
import { defaultSidebarConfig } from "~/config/sidebar.config";
import { cn } from "~/lib/utils/utils";

export interface SidebarItem {
  title: string;
  url: string;
  icon: ReactNode;
  badge?: string;
  children?: SidebarItem[];
  description?: string;
}

export interface ChatConversation {
  id: string;
  title: string;
  model?: string;
  provider?: string;
  isArchived: boolean;
  lastMessageAt?: string;
  createdAt: string;
  updatedAt: string;
}

export interface SidebarProps {
  items?: SidebarItem[];
  className?: string;
  onClose?: () => void;
  showCloseButton?: boolean;
  title?: string;
  logo?: ReactNode;
  // Chat-specific props
  mode?: "navigation" | "chat";
  conversations?: ChatConversation[];
  currentConversationId?: string;
  onNewChat?: () => void;
  onSelectConversation?: (id: string) => void;
  onArchiveConversation?: (id: string) => void;
  isLoadingConversations?: boolean;
}

export default function Sidebar({
  items = defaultSidebarConfig,
  className = "",
  onClose,
  showCloseButton = false,
  title = "Navigation",
  logo,
  mode = "navigation",
  conversations = [],
  currentConversationId,
  onNewChat,
  onSelectConversation,
  onArchiveConversation,
  isLoadingConversations = false,
}: SidebarProps) {
  const [expandedItems, setExpandedItems] = useState<string[]>([]);
  const location = useLocation();

  const isActive = (url: string) => {
    if (url === "/") {
      return location.pathname === "/";
    }
    return location.pathname.startsWith(url);
  };

  const toggleExpanded = (title: string) => {
    setExpandedItems((prev) =>
      prev.includes(title) ? prev.filter((item) => item !== title) : [...prev, title]
    );
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60);

    if (diffInHours < 24) {
      return date.toLocaleTimeString([], { hour: "2-digit", minute: "2-digit" });
    } else if (diffInHours < 24 * 7) {
      return date.toLocaleDateString([], { weekday: "short" });
    } else {
      return date.toLocaleDateString([], { month: "short", day: "numeric" });
    }
  };

  const handleArchiveConversation = (conversationId: string, e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    onArchiveConversation?.(conversationId);
  };

  const renderSidebarItem = (item: SidebarItem, level = 0) => {
    const hasChildren = item.children && item.children.length > 0;
    const isExpanded = expandedItems.includes(item.title);
    const active = isActive(item.url);

    return (
      <div key={item.title} className="w-full">
        <div
          className={cn(
            "flex items-center justify-between w-full rounded-lg transition-all duration-200 group",
            level === 0 ? "px-3 py-2.5" : "px-6 py-2",
            active
              ? "bg-primary/10 text-primary border-r-2 border-primary"
              : "hover:bg-muted/50 text-muted-foreground hover:text-foreground"
          )}
        >
          <Link to={item.url} className="flex items-center gap-3 flex-1 min-w-0" onClick={onClose}>
            <div
              className={cn(
                "flex-shrink-0 transition-colors",
                active ? "text-primary" : "text-muted-foreground group-hover:text-foreground"
              )}
            >
              {item.icon}
            </div>
            <div className="flex-1 min-w-0">
              <div className="flex items-center gap-2">
                <span
                  className={cn(
                    "font-medium truncate",
                    level === 0 ? "text-sm" : "text-xs",
                    active ? "text-primary" : ""
                  )}
                >
                  {item.title}
                </span>
                {item.badge && (
                  <Badge variant="secondary" className="text-xs px-1.5 py-0.5">
                    {item.badge}
                  </Badge>
                )}
              </div>
              {item.description && level === 0 && (
                <p className="text-xs text-muted-foreground truncate mt-0.5">{item.description}</p>
              )}
            </div>
          </Link>
          {hasChildren && (
            <Button
              variant="ghost"
              size="sm"
              className="h-6 w-6 p-0 flex-shrink-0"
              onClick={() => toggleExpanded(item.title)}
            >
              {isExpanded ? (
                <ChevronDown className="h-3 w-3" />
              ) : (
                <ChevronRight className="h-3 w-3" />
              )}
            </Button>
          )}
        </div>
        {hasChildren && isExpanded && (
          <div className="mt-1 space-y-1">
            {item.children?.map((child) => renderSidebarItem(child, level + 1))}
          </div>
        )}
      </div>
    );
  };

  if (mode === "chat") {
    return (
      <div className={cn("flex flex-col h-full bg-background border-r", className)}>
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b">
          <div className="flex items-center gap-2">
            <div className="w-8 h-8 bg-gradient-to-br from-blue-600 to-purple-600 rounded-lg flex items-center justify-center">
              <Sparkles className="h-4 w-4 text-white" />
            </div>
            <span className="font-bold text-lg">{title}</span>
          </div>
          {showCloseButton && onClose && (
            <Button variant="ghost" size="icon" onClick={onClose}>
              <X className="h-5 w-5" />
            </Button>
          )}
        </div>

        {/* New Chat Button */}
        <div className="p-4 border-b">
          <Button
            onClick={onNewChat}
            className="w-full justify-start gap-2 bg-gradient-to-r from-blue-500 to-purple-500 hover:from-blue-600 hover:to-purple-600 text-white"
          >
            <Plus className="h-4 w-4" />
            New Chat
          </Button>
        </div>

        {/* Conversations List */}
        <div className="flex-1 overflow-y-auto">
          {isLoadingConversations ? (
            <div className="p-4 text-center text-muted-foreground">Loading conversations...</div>
          ) : conversations.length === 0 ? (
            <div className="p-4 text-center text-muted-foreground">
              <MessageSquare className="h-8 w-8 mx-auto mb-2 opacity-50" />
              <p className="text-sm">No conversations yet</p>
              <p className="text-xs mt-1">Start a new chat to begin</p>
            </div>
          ) : (
            <div className="p-2">
              {conversations.map((conversation) => (
                <div
                  key={conversation.id}
                  className={cn(
                    "group relative flex items-center gap-3 p-3 rounded-lg cursor-pointer transition-all duration-200 mb-1",
                    currentConversationId === conversation.id
                      ? "bg-primary/10 text-primary border-r-2 border-primary"
                      : "hover:bg-muted/50 text-muted-foreground hover:text-foreground"
                  )}
                  onClick={() => onSelectConversation?.(conversation.id)}
                  onKeyDown={(e) => {
                    if (e.key === 'Enter' || e.key === ' ') {
                      e.preventDefault();
                      onSelectConversation?.(conversation.id);
                    }
                  }}
                  role="button"
                  tabIndex={0}
                  aria-label={`Select conversation: ${conversation.title}`}
                >
                  <MessageSquare className="h-4 w-4 flex-shrink-0" />
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center justify-between">
                      <p className="font-medium text-sm truncate">{conversation.title}</p>
                      <span className="text-xs text-muted-foreground flex-shrink-0 ml-2">
                        {formatDate(conversation.lastMessageAt || conversation.createdAt)}
                      </span>
                    </div>
                    {conversation.provider && (
                      <div className="flex items-center gap-1 mt-1">
                        <Badge variant="secondary" className="text-xs px-1.5 py-0.5">
                          {conversation.provider}
                        </Badge>
                        {conversation.model && (
                          <span className="text-xs text-muted-foreground">
                            {conversation.model}
                          </span>
                        )}
                      </div>
                    )}
                  </div>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-6 w-6 p-0 opacity-0 group-hover:opacity-100 transition-opacity"
                    onClick={(e) => handleArchiveConversation(conversation.id, e)}
                  >
                    <Trash2 className="h-3 w-3" />
                  </Button>
                </div>
              ))}
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="p-4 border-t">
          <Link
            to="/settings"
            className="flex items-center gap-3 px-3 py-2 rounded-lg hover:bg-muted/50 text-muted-foreground hover:text-foreground transition-colors"
            onClick={onClose}
          >
            <Settings className="h-5 w-5" />
            <span className="font-medium text-sm">Settings</span>
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className={cn("flex flex-col h-full bg-background border-r", className)}>
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b">
        <div className="flex items-center gap-2">
          {logo || (
            <div className="w-8 h-8 bg-gradient-to-br from-blue-600 to-purple-600 rounded-lg flex items-center justify-center">
              <Sparkles className="h-4 w-4 text-white" />
            </div>
          )}
          <span className="font-bold text-lg">{title}</span>
        </div>
        {showCloseButton && onClose && (
          <Button variant="ghost" size="icon" onClick={onClose}>
            <X className="h-5 w-5" />
          </Button>
        )}
      </div>

      {/* Navigation Items */}
      <nav className="flex-1 overflow-y-auto p-4">
        <div className="space-y-2">{items.map((item) => renderSidebarItem(item))}</div>
      </nav>

      {/* Footer */}
      <div className="p-4 border-t">
        <Link
          to="/settings"
          className="flex items-center gap-3 px-3 py-2 rounded-lg hover:bg-muted/50 text-muted-foreground hover:text-foreground transition-colors"
          onClick={onClose}
        >
          <Settings className="h-5 w-5" />
          <span className="font-medium text-sm">Settings</span>
        </Link>
      </div>
    </div>
  );
}
