import { useSearchParams } from "@remix-run/react";
import { <PERSON><PERSON>, Loader2, <PERSON>f<PERSON><PERSON><PERSON>, Send, Spark<PERSON>, User } from "lucide-react";
import { useEffect, useRef, useState } from "react";
import { Badge } from "~/components/ui/badge";
import { Button } from "~/components/ui/button";
import { Textarea } from "~/components/ui/textarea";

interface Message {
  id: string;
  role: "user" | "assistant";
  content: string;
  createdAt: string;
  provider?: string;
  model?: string;
  tokenCount?: number;
  metadata?: any;
  imageUrl?: string;
}

interface Conversation {
  id: string;
  title: string;
  model?: string;
  provider?: string;
  isArchived: boolean;
  lastMessageAt?: string;
  createdAt: string;
  updatedAt: string;
}

interface AIChatInterfaceProps {
  className?: string;
  conversationId?: string;
}

export default function AIChatInterface({
  className,
  conversationId: propConversationId,
}: AIChatInterfaceProps) {
  const [searchParams, setSearchParams] = useSearchParams();
  const [messages, setMessages] = useState<Message[]>([]);
  const [input, setInput] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [isImageLoading, setIsImageLoading] = useState(false); // Added image loading state
  const [currentConversation, setCurrentConversation] = useState<Conversation | null>(null);
  const [isLoadingConversation, setIsLoadingConversation] = useState(false);
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // Get conversation ID from props or URL params
  const conversationId = propConversationId || searchParams.get("conversation");

  // 自动滚动到底部
  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  // Load conversation when conversationId changes
  useEffect(() => {
    if (conversationId) {
      loadConversation(conversationId);
    } else {
      // Clear messages if no conversation selected
      setMessages([]);
      setCurrentConversation(null);
    }
  }, [conversationId]);

  // Load conversation and its messages
  const loadConversation = async (id: string) => {
    setIsLoadingConversation(true);
    try {
      const response = await fetch(`/api/chat/conversations/${id}`);
      if (!response.ok) {
        throw new Error("Failed to load conversation");
      }
      const data = await response.json();
      console.log("Load conversation data:", data);

      // Check for the correct response format from respData
      if (data.code === 0 && data.data) {
        setCurrentConversation(data.data.conversation);
        setMessages(data.data.messages || []);
      }
    } catch (error) {
      console.error("Failed to load conversation:", error);
    } finally {
      setIsLoadingConversation(false);
    }
  };

  // Create a new conversation
  const createNewConversation = async (firstMessage: string) => {
    try {
      const response = await fetch("/api/chat/conversations", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          action: "create",
          title: firstMessage.slice(0, 50) + (firstMessage.length > 50 ? "..." : ""),
        }),
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error("Create conversation failed:", response.status, errorText);
        throw new Error(`Failed to create conversation: ${response.status}`);
      }

      const responseText = await response.text();
      console.log("Create conversation response:", responseText);

      if (!responseText || responseText === "undefined") {
        throw new Error("Empty response from server");
      }

      const data = JSON.parse(responseText);
      console.log("Parsed conversation data:", data);

      // Check for the correct response format from respData
      if (data.code === 0 && data.data) {
        const newConversation = data.data.conversation;
        setCurrentConversation(newConversation);
        // Update URL to include conversation ID
        setSearchParams({ conversation: newConversation.id });
        return newConversation.id;
      } else {
        throw new Error(data.message || "Unknown error creating conversation");
      }
    } catch (error) {
      console.error("Failed to create conversation:", error);
    }
    return null;
  };

  // Save message to database
  const saveMessage = async (
    conversationId: string,
    message: Omit<Message, "id" | "createdAt">
  ) => {
    try {
      const response = await fetch("/api/chat/conversations", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          action: "add-message",
          conversationId,
          message,
        }),
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error("Save message failed:", response.status, errorText);
        throw new Error(`Failed to save message: ${response.status}`);
      }

      const responseText = await response.text();
      console.log("Save message response:", responseText);

      if (!responseText || responseText === "undefined") {
        throw new Error("Empty response from server");
      }

      const data = JSON.parse(responseText);
      console.log("Parsed save message data:", data);

      // Check for the correct response format from respData
      return data.code === 0 && data.data ? data.data.message : null;
    } catch (error) {
      console.error("Failed to save message:", error);
      return null;
    }
  };

  // 处理文本生成
  const handleTextGeneration = async (prompt: string, currentConvId?: string) => {
    console.log("🤖 Starting text generation with:", {
      prompt: prompt.substring(0, 50) + "...",
      currentConvId,
    });

    try {
      // Try Cloudflare AI first, fallback to OpenAI
      console.log("📡 Calling Cloudflare AI API...");
      let response = await fetch("/api/ai/cloudflare", {
        method: "POST",
        body: (() => {
          const formData = new FormData();
          formData.append("action", "generate-text");
          formData.append("model", "llama-3.2-3b");
          formData.append("prompt", prompt);
          formData.append("maxTokens", "2048");
          formData.append("temperature", "0.7");
          return formData;
        })(),
      });

      let result: any;
      let provider = "cloudflare";
      let model = "llama-3.2-3b";

      console.log("📡 Cloudflare AI response status:", response.status, response.statusText);

      if (!response.ok) {
        // Fallback to OpenAI
        console.log("❌ Cloudflare AI failed, trying OpenAI...");
        const errorText = await response.text();
        console.log("Cloudflare error details:", errorText);

        response = await fetch("/api/ai/generate-text", {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({
            prompt,
            provider: "openai",
            model: "gpt-4o-mini",
          }),
        });
        provider = "openai";
        model = "gpt-4o-mini";

        console.log("📡 OpenAI response status:", response.status, response.statusText);
      }

      if (!response.ok) {
        const errorText = await response.text();
        console.log("❌ Both providers failed. Last error:", errorText);
        throw new Error(`Both AI providers failed. Status: ${response.status}`);
      }

      const responseText = await response.text();
      console.log("AI response text:", responseText);

      if (!responseText || responseText === "undefined") {
        throw new Error("Empty response from AI service");
      }

      result = JSON.parse(responseText);
      console.log("✅ AI response received:", { provider, model, resultKeys: Object.keys(result) });

      let content: string;
      if (provider === "cloudflare") {
        content = result.data?.result?.response || result.data?.result || "No response generated";
        console.log("📝 Cloudflare content extracted:", content.substring(0, 100) + "...");
      } else {
        content = result.data?.text || "No response generated";
        console.log("📝 OpenAI content extracted:", content.substring(0, 100) + "...");
      }

      const assistantMessage: Message = {
        id: Date.now().toString() + "-assistant",
        role: "assistant",
        content,
        createdAt: new Date().toISOString(),
        provider,
        model,
      };

      console.log("💬 Adding assistant message to UI");
      setMessages((prev) => [...prev, assistantMessage]);

      // Save to database if we have a conversation
      if (currentConvId) {
        console.log("💾 Saving assistant message to database...");
        await saveMessage(currentConvId, {
          role: "assistant",
          content,
          provider,
          model,
        });
        console.log("✅ Assistant message saved to database");
      }

      console.log("🎉 Text generation completed successfully");
    } catch (error) {
      console.error("❌ Text generation error:", error);
      const errorMessage: Message = {
        id: Date.now().toString() + "-error",
        role: "assistant",
        content: "抱歉，生成文本时出现了错误。请稍后再试。",
        createdAt: new Date().toISOString(),
      };
      setMessages((prev) => [...prev, errorMessage]);
    }
  };

  // Handle image generation
  const handleImageGeneration = async (prompt: string, currentConvId?: string) => {
    console.log("🖼️ Starting image generation with:", {
      prompt: prompt.substring(0, 50) + "...",
      currentConvId,
    });
    setIsImageLoading(true);

    try {
      const response = await fetch("/api/ai/generate-image", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          prompt,
          provider: "cloudflare", // Specify Cloudflare
          // Model can be defaulted by backend if not specified here
        }),
      });

      console.log("🖼️ Image generation API response status:", response.status, response.statusText);

      if (!response.ok) {
        const errorText = await response.text();
        console.log("❌ Image generation failed:", errorText);
        throw new Error(`Image generation failed. Status: ${response.status}`);
      }

      const result = await response.json();
      console.log("✅ Image generation response received:", { resultKeys: Object.keys(result) });

      if (
        result.code !== 0 ||
        !result.data ||
        !result.data.images ||
        result.data.images.length === 0
      ) {
        throw new Error("Invalid response from image generation API or no images generated.");
      }

      const imageUrl = result.data.images[0]; // Assuming the first image is the one we want

      const assistantMessage: Message = {
        id: Date.now().toString() + "-assistant-image",
        role: "assistant",
        content: `Here's the image for "${prompt}":`, // Or some other relevant text
        imageUrl: imageUrl,
        createdAt: new Date().toISOString(),
        provider: "cloudflare", // Assuming provider is cloudflare
        model: result.data.model || "@cf/stabilityai/stable-diffusion-xl-base-1.0", // Store model if available
      };

      console.log("💬 Adding assistant image message to UI");
      setMessages((prev) => [...prev, assistantMessage]);

      // Save to database if we have a conversation
      if (currentConvId) {
        console.log("💾 Saving assistant image message to database...");
        await saveMessage(currentConvId, {
          role: "assistant",
          content: assistantMessage.content,
          imageUrl: assistantMessage.imageUrl,
          provider: assistantMessage.provider,
          model: assistantMessage.model,
        });
        console.log("✅ Assistant image message saved to database");
      }
      console.log("🎉 Image generation completed successfully");
    } catch (error) {
      console.error("❌ Image generation error:", error);
      const errorMessage: Message = {
        id: Date.now().toString() + "-error-image",
        role: "assistant",
        content: "抱歉，生成图片时出现了错误。请稍后再试。",
        createdAt: new Date().toISOString(),
      };
      setMessages((prev) => [...prev, errorMessage]);
    } finally {
      setIsImageLoading(false);
    }
  };

  // 提交消息
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    console.log("handleSubmit called", { input: input.trim(), isLoading, isImageLoading });

    if (!input.trim() || isLoading || isImageLoading) {
      console.log("Submit blocked:", { inputEmpty: !input.trim(), isLoading, isImageLoading });
      return;
    }

    const messageContent = input.trim();
    setInput("");

    try {
      let convId = currentConversation?.id;
      console.log("Current conversation ID:", convId);

      // Create new conversation if none exists
      if (!convId) {
        console.log("Creating new conversation...");
        convId = await createNewConversation(
          messageContent.startsWith("/image ") ? messageContent.substring(7) : messageContent
        );
        console.log("New conversation created with ID:", convId);
        if (!convId) {
          throw new Error("Failed to create conversation");
        }
      }

      // Create user message
      const userMessage: Message = {
        id: Date.now().toString() + "-user",
        role: "user",
        content: messageContent,
        createdAt: new Date().toISOString(),
      };

      console.log("Adding user message:", userMessage);
      setMessages((prev) => [...prev, userMessage]);

      // Save user message to database
      console.log("Saving user message to database...");
      await saveMessage(convId, {
        role: "user",
        content: messageContent,
      });

      // Check if it's an image generation command
      if (messageContent.startsWith("/image ")) {
        const prompt = messageContent.substring(7).trim(); // Extract prompt after "/image "
        if (prompt) {
          console.log("🖼️ Image generation command detected. Prompt:", prompt);
          await handleImageGeneration(prompt, convId);
        } else {
          // Handle empty prompt for image generation
          const errorMessage: Message = {
            id: Date.now().toString() + "-error-image-prompt",
            role: "assistant",
            content: "请输入图片描述。例如: /image 一只可爱的猫",
            createdAt: new Date().toISOString(),
          };
          setMessages((prev) => [...prev, errorMessage]);
        }
      } else {
        // Generate AI text response
        console.log("Generating AI text response...");
        setIsLoading(true);
        await handleTextGeneration(messageContent, convId);
      }
    } catch (error) {
      console.error("Failed to send message:", error);
      // Add error message
      const errorMessage: Message = {
        id: Date.now().toString() + "-error",
        role: "assistant",
        content: "抱歉，发送消息时出现了错误。请稍后再试。",
        createdAt: new Date().toISOString(),
      };
      setMessages((prev) => [...prev, errorMessage]);
    } finally {
      // General loading state for text should be handled by handleTextGeneration
      // setIsLoading(false); // This might be set too early if image generation is also loading
      if (!messageContent.startsWith("/image ")) {
        setIsLoading(false);
      }
    }
  };

  // 复制消息内容
  const copyMessage = (content: string) => {
    navigator.clipboard.writeText(content);
  };

  // 重新生成
  const regenerateMessage = async (prompt: string) => {
    if (isLoading || !currentConversation) return;

    setIsLoading(true);
    try {
      await handleTextGeneration(prompt, currentConversation.id);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className={`flex flex-col h-full bg-white dark:bg-gray-900 ${className}`}>
      {/* 消息区域 */}
      <div className="flex-1 overflow-y-auto">
        <div className="max-w-3xl mx-auto">
          {isLoadingConversation ? (
            <div className="flex flex-col items-center justify-center h-full py-8">
              <Loader2 className="w-8 h-8 animate-spin text-blue-500 mb-4" />
              <p className="text-gray-500 dark:text-gray-400">Loading conversation...</p>
            </div>
          ) : messages.length === 0 ? (
            <div className="flex flex-col items-center justify-center h-full py-8">
              <h2 className="text-2xl font-semibold text-gray-900 dark:text-white mb-2">
                {currentConversation ? currentConversation.title : "Hello"}
              </h2>
              <p className="text-gray-500 dark:text-gray-400 text-center max-w-md mb-4">
                {currentConversation ? "Continue your conversation" : "What can I do for you?"}
              </p>
            </div>
          ) : (
            <div className="space-y-6 px-4 py-6">
              {messages.map((message) => (
                <div key={message.id} className="group">
                  <div className="flex gap-4">
                    <div className="w-8 h-8 flex-shrink-0 rounded-full flex items-center justify-center">
                      {message.role === "assistant" ? (
                        <div className="w-8 h-8 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center">
                          <Sparkles className="w-4 h-4 text-white" />
                        </div>
                      ) : (
                        <div className="w-8 h-8 bg-gray-200 dark:bg-gray-700 rounded-full flex items-center justify-center">
                          <User className="w-4 h-4 text-gray-600 dark:text-gray-300" />
                        </div>
                      )}
                    </div>
                    <div className="flex-1 space-y-2">
                      <div className="flex items-center gap-2">
                        <span className="font-medium text-sm text-gray-900 dark:text-white">
                          {message.role === "assistant" ? "ChatGPT" : "You"}
                        </span>
                        {message.provider && (
                          <Badge variant="secondary" className="text-xs">
                            {message.provider}
                          </Badge>
                        )}
                      </div>
                      <div className="prose prose-sm max-w-none dark:prose-invert">
                        <p className="whitespace-pre-wrap text-gray-900 dark:text-gray-100 leading-relaxed">
                          {message.content}
                        </p>
                        {message.imageUrl && (
                          <div className="mt-2">
                            <img
                              src={message.imageUrl}
                              alt="Generated image"
                              className="max-w-full h-auto rounded-lg border border-gray-200 dark:border-gray-700"
                              style={{ maxHeight: "400px" }} // Optional: constrain max height
                            />
                          </div>
                        )}
                      </div>
                      {message.role === "assistant" &&
                        !message.imageUrl && ( // Hide controls for image messages for now
                          <div className="flex gap-1 opacity-0 group-hover:opacity-100 transition-opacity">
                            <Button
                              size="sm"
                              variant="ghost"
                              onClick={() => copyMessage(message.content)}
                              className="h-8 w-8 p-0 hover:bg-gray-100 dark:hover:bg-gray-800"
                            >
                              <Copy className="w-4 h-4" />
                            </Button>
                            <Button
                              size="sm"
                              variant="ghost"
                              onClick={() => {
                                // Find the preceding user message to use as prompt for regeneration
                                let userPrompt = "";
                                for (let i = messages.indexOf(message) - 1; i >= 0; i--) {
                                  if (messages[i].role === "user") {
                                    userPrompt = messages[i].content;
                                    // Check if user prompt was an image command, if so, don't allow text regeneration
                                    if (userPrompt.startsWith("/image ")) {
                                      userPrompt = ""; // Or show a message that image regeneration is not supported this way
                                    }
                                    break;
                                  }
                                }
                                if (userPrompt) regenerateMessage(userPrompt);
                                // else console.log("Could not find user prompt for regeneration or was image command")
                              }}
                              className="h-8 w-8 p-0 hover:bg-gray-100 dark:hover:bg-gray-800"
                            >
                              <RefreshCw className="w-4 h-4" />
                            </Button>
                          </div>
                        )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}

          {(isLoading || isImageLoading) && (
            <div className="px-4 py-6">
              <div className="group">
                <div className="flex gap-4">
                  <div className="w-8 h-8 flex-shrink-0 rounded-full flex items-center justify-center">
                    <div className="w-8 h-8 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center">
                      <Sparkles className="w-4 h-4 text-white" />
                    </div>
                  </div>
                  <div className="flex-1 space-y-2">
                    <div className="flex items-center gap-2">
                      <span className="font-medium text-sm text-gray-900 dark:text-white">
                        ChatGPT
                      </span>
                    </div>
                    <div className="flex items-center gap-2">
                      <div className="flex space-x-1">
                        <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce [animation-delay:-0.3s]" />
                        <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce [animation-delay:-0.15s]" />
                        <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" />
                      </div>
                      <span className="text-sm text-gray-500">
                        {isImageLoading ? "Generating image..." : "Thinking..."}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}

          <div ref={messagesEndRef} />
        </div>
      </div>

      {/* 输入区域 */}
      <div className="bg-white dark:bg-gray-900">
        <div className="max-w-3xl mx-auto px-4 pb-4">
          <form onSubmit={handleSubmit}>
            <div className="relative flex items-end gap-2">
              <div className="flex-1 relative">
                <Textarea
                  ref={textareaRef}
                  value={input}
                  onChange={(e) => setInput(e.target.value)}
                  onKeyDown={(e) => {
                    if (e.key === "Enter" && !e.shiftKey) {
                      e.preventDefault();
                      handleSubmit(e);
                    }
                  }}
                  placeholder="Message ChatGPT or type /image <prompt>"
                  className="min-h-[78px] max-h-[200px] resize-none pr-12 text-base border-2 border-blue-200 dark:border-blue-700 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:focus:border-blue-400 bg-white/70 dark:bg-gray-800/70 backdrop-blur-sm shadow-lg hover:shadow-xl transition-all duration-300 hover:border-blue-300 dark:hover:border-blue-600 relative z-0"
                  disabled={isLoading || isImageLoading}
                  rows={3}
                />
                <button
                  type="submit"
                  disabled={!input.trim() || isLoading || isImageLoading}
                  className="absolute right-2 bottom-2 h-8 w-8 p-0 rounded-lg bg-gradient-to-r from-blue-500 to-purple-500 hover:from-blue-600 hover:to-purple-600 disabled:opacity-50 disabled:cursor-not-allowed shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105 z-10 flex items-center justify-center border-0"
                >
                  {isLoading || isImageLoading ? (
                    <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                  ) : (
                    <Send className="w-4 h-4 text-white" />
                  )}
                </button>
              </div>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
}
