import type { MetaFunction } from "@remix-run/cloudflare";
import { ArrowRight, Code, Globe, Heart, Lightbulb, Target, Users, Zap } from "lucide-react";
import UnifiedLayout from "~/components/layout/unified-layout";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "~/components/ui/card";

export const meta: MetaFunction = () => {
  const title = "About Us - AI SaaS Platform";
  const description = "Learn about our mission to democratize AI technology and make it accessible to developers worldwide.";

  return [
    { title },
    { name: "description", content: description },
    { property: "og:title", content: title },
    { property: "og:description", content: description },
    { property: "og:type", content: "website" },
    { name: "twitter:card", content: "summary_large_image" },
    { name: "twitter:title", content: title },
    { name: "twitter:description", content: description },
  ];
};

const values = [
  {
    id: "mission",
    icon: <Target className="h-6 w-6" />,
    title: "Mission-Driven",
    description: "Making AI technology accessible to developers and businesses worldwide.",
  },
  {
    id: "community",
    icon: <Users className="h-6 w-6" />,
    title: "Community First",
    description: "Building tools that empower creators and drive innovation forward.",
  },
  {
    id: "innovation",
    icon: <Lightbulb className="h-6 w-6" />,
    title: "Innovation",
    description: "Staying at the forefront of AI technology and exploring new possibilities.",
  },
  {
    id: "quality",
    icon: <Heart className="h-6 w-6" />,
    title: "Quality Focus",
    description: "Crafting every feature with care and designing exceptional user experiences.",
  },
];

const features = [
  {
    id: "developer",
    icon: <Code className="h-6 w-6" />,
    title: "Developer-First",
    description: "Clean APIs, comprehensive documentation, and powerful development tools.",
  },
  {
    id: "performance",
    icon: <Zap className="h-6 w-6" />,
    title: "Lightning Fast",
    description: "Optimized performance with edge computing and global distribution.",
  },
  {
    id: "scale",
    icon: <Globe className="h-6 w-6" />,
    title: "Global Scale",
    description: "Worldwide availability with 99.9% uptime and enterprise reliability.",
  },
];

const stats = [
  { id: "users", label: "Active Users", value: "10,000+" },
  { id: "requests", label: "API Requests", value: "1M+" },
  { id: "countries", label: "Countries", value: "50+" },
  { id: "uptime", label: "Uptime", value: "99.9%" },
];

export default function AboutPage() {
  return (
    <UnifiedLayout
      showHeader={true}
      showFooter={true}
      showSidebar={false}
      containerSize="full"
      hero={{
        badge: {
          text: "About Our Mission",
        },
        title: "Democratizing AI for Everyone",
        description:
          "We believe AI should be accessible, powerful, and easy to use. Our platform empowers developers and businesses to harness the full potential of artificial intelligence.",
        buttons: [
          {
            text: "Get Started",
            href: "/",
            variant: "primary",
            icon: <ArrowRight className="h-5 w-5" />,
          },
        ],
        trustIndicators: ["Trusted by 10,000+ developers", "99.9% uptime", "Enterprise ready"],
      }}
    >
      {/* Stats Section */}
      <section className="py-16 bg-muted/50">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
            {stats.map((stat) => (
              <div key={stat.id} className="text-center">
                <div className="text-3xl lg:text-4xl font-bold text-foreground mb-2">
                  {stat.value}
                </div>
                <div className="text-sm font-medium text-muted-foreground uppercase tracking-wider">
                  {stat.label}
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Values Section */}
      <section className="py-16 bg-background">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-foreground mb-4">Our Values</h2>
            <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
              Core principles that guide everything we do.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {values.map((value) => (
              <Card key={value.id} className="p-6">
                <CardHeader>
                  <div className="w-12 h-12 bg-primary rounded-lg flex items-center justify-center text-white mb-4">
                    {value.icon}
                  </div>
                  <CardTitle className="text-xl">{value.title}</CardTitle>
                </CardHeader>
                <CardContent>
                  <CardDescription className="text-base">
                    {value.description}
                  </CardDescription>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-16 bg-muted/50">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-foreground mb-4">Why Choose Us</h2>
            <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
              Built with modern developers and businesses in mind.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {features.map((feature) => (
              <Card key={feature.id} className="p-6 text-center">
                <CardHeader>
                  <div className="w-12 h-12 bg-primary rounded-lg flex items-center justify-center text-white mx-auto mb-4">
                    {feature.icon}
                  </div>
                  <CardTitle className="text-xl">{feature.title}</CardTitle>
                </CardHeader>
                <CardContent>
                  <CardDescription className="text-base">{feature.description}</CardDescription>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 bg-background">
        <div className="container mx-auto px-4 text-center">
          <div className="max-w-2xl mx-auto">
            <h2 className="text-3xl font-bold text-foreground mb-4">
              Ready to Get Started?
            </h2>
            <p className="text-lg text-muted-foreground mb-8">
              Join thousands of developers building AI-powered applications.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <a
                href="/"
                className="inline-flex items-center justify-center gap-2 px-6 py-3 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 transition-colors"
              >
                Get Started
                <ArrowRight className="h-4 w-4" />
              </a>
            </div>
          </div>
        </div>
      </section>
    </UnifiedLayout>
  );
}
