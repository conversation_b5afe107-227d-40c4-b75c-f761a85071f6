// src/lib/ai/errors.ts

import { AIError } from "ai"; // Import base AIError from Vercel AI SDK if available and suitable

// --- Custom Error Classes ---

export class AIConfigurationError extends Error {
  constructor(message: string) {
    super(message);
    this.name = "AIConfigurationError";
  }
}

export class AIProviderError extends Error {
  public readonly providerId?: string;
  public readonly modelId?: string;
  public readonly originalError?: any;
  public readonly statusCode?: number;

  constructor(params: {
    message: string;
    providerId?: string;
    modelId?: string;
    originalError?: any;
    statusCode?: number;
  }) {
    super(params.message);
    this.name = "AIProviderError";
    this.providerId = params.providerId;
    this.modelId = params.modelId;
    this.originalError = params.originalError;
    this.statusCode = params.statusCode;
  }
}

export class AIResponseError extends AIProviderError {
  constructor(params: {
    message: string;
    providerId?: string;
    modelId?: string;
    originalError?: any;
    statusCode?: number;
  }) {
    super(params);
    this.name = "AIResponseError";
  }
}

export class AITimeoutError extends AIProviderError {
  constructor(params: {
    message: string;
    providerId?: string;
    modelId?: string;
    originalError?: any;
  }) {
    super(params);
    this.name = "AITimeoutError";
  }
}

export class AIUnsupportedOperationError extends AIProviderError {
  constructor(params: {
    message: string;
    providerId?: string;
    modelId?: string;
  }) {
    super(params);
    this.name = "AIUnsupportedOperationError";
  }
}

// --- Unified Error Handler Function ---

export interface AIErrorContext {
  operation: string; // e.g., "text-generation", "image-generation"
  providerId?: string;
  modelId?: string;
  // Add any other relevant context
}

export interface StandardizedAIErrorResponse {
  success: false;
  error: {
    message: string;
    code?: string; // e.g., "PROVIDER_ERROR", "CONFIGURATION_ERROR"
    details?: any;
    provider?: string;
    model?: string;
    operation?: string;
  };
}

export function handleAiError(error: any, context: AIErrorContext): StandardizedAIErrorResponse {
  console.error(
    `AI Error during operation '${context.operation}' (Provider: ${context.providerId || "N/A"}, Model: ${context.modelId || "N/A"}):`,
    error
  );

  let response: StandardizedAIErrorResponse;

  if (error instanceof AIConfigurationError) {
    response = {
      success: false,
      error: {
        message: error.message,
        code: "CONFIGURATION_ERROR",
        operation: context.operation,
      },
    };
  } else if (error instanceof AITimeoutError) {
    response = {
      success: false,
      error: {
        message: error.message || "The AI operation timed out.",
        code: "TIMEOUT_ERROR",
        provider: error.providerId,
        model: error.modelId,
        details: error.originalError,
        operation: context.operation,
      },
    };
  } else if (error instanceof AIUnsupportedOperationError) {
    response = {
      success: false,
      error: {
        message: error.message,
        code: "UNSUPPORTED_OPERATION_ERROR",
        provider: error.providerId,
        model: error.modelId,
        operation: context.operation,
      },
    };
  } else if (error instanceof AIResponseError) {
    response = {
      success: false,
      error: {
        message: error.message || "The AI provider returned an error response.",
        code: "PROVIDER_RESPONSE_ERROR",
        provider: error.providerId,
        model: error.modelId,
        details: error.originalError,
        operation: context.operation,
      },
    };
  } else if (error instanceof AIProviderError) {
    response = {
      success: false,
      error: {
        message: error.message || "An unspecified error occurred with the AI provider.",
        code: "PROVIDER_ERROR",
        provider: error.providerId,
        model: error.modelId,
        details: error.originalError,
        operation: context.operation,
      },
    };
  } else if (error instanceof AIError) {
    // Vercel AI SDK's base error
    response = {
      success: false,
      error: {
        message: error.message || "An error occurred with the AI SDK.",
        code: "AI_SDK_ERROR",
        details: { name: error.name, cause: error.cause },
        operation: context.operation,
        provider: context.providerId, // AIError might not have these, so use context
        model: context.modelId,
      },
    };
  } else if (error instanceof Error) {
    response = {
      success: false,
      error: {
        message: error.message || "An unexpected error occurred.",
        code: "UNEXPECTED_ERROR",
        details: error.stack,
        operation: context.operation,
      },
    };
  } else {
    response = {
      success: false,
      error: {
        message: "An unknown error occurred.",
        code: "UNKNOWN_ERROR",
        details: error,
        operation: context.operation,
      },
    };
  }
  return response;
}

// Helper to easily throw AIProviderError from caught provider errors
export function throwAIProviderError(
  originalError: any,
  context: { providerId: string; modelId?: string; message?: string; statusCode?: number }
): never {
  const defaultMessage = `Error with provider ${context.providerId}${context.modelId ? ` (model: ${context.modelId})` : ""}.`;
  throw new AIProviderError({
    message: context.message || originalError?.message || defaultMessage,
    providerId: context.providerId,
    modelId: context.modelId,
    originalError: originalError,
    statusCode: context.statusCode || originalError?.status || originalError?.statusCode,
  });
}

export function throwAIResponseError(
  originalError: any,
  context: { providerId: string; modelId?: string; message?: string; statusCode?: number }
): never {
  const defaultMessage = `Provider ${context.providerId} returned an invalid response${context.modelId ? ` for model ${context.modelId}` : ""}.`;
  throw new AIResponseError({
    message: context.message || originalError?.message || defaultMessage,
    providerId: context.providerId,
    modelId: context.modelId,
    originalError: originalError,
    statusCode: context.statusCode || originalError?.status || originalError?.statusCode,
  });
}
